name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build:
    name: Build for ${{ matrix.target }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-latest
            name: ccline-linux-x64.tar.gz
          - target: x86_64-pc-windows-gnu
            os: ubuntu-latest
            name: ccline-windows-x64.zip
          - target: x86_64-apple-darwin
            os: macos-latest
            name: ccline-macos-x64.tar.gz
          - target: aarch64-apple-darwin
            os: macos-latest
            name: ccline-macos-arm64.tar.gz

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Install cross-compilation tools
      if: matrix.target == 'x86_64-pc-windows-gnu'
      run: |
        sudo apt-get update
        sudo apt-get install -y mingw-w64

    - name: Build binary
      run: cargo build --release --target ${{ matrix.target }}

    - name: Package Linux/macOS
      if: matrix.os != 'windows-latest' && matrix.target != 'x86_64-pc-windows-gnu'
      run: |
        mkdir -p dist
        cp target/${{ matrix.target }}/release/ccometixline dist/ccline
        cd dist
        tar czf ../${{ matrix.name }} ccline

    - name: Package Windows
      if: matrix.target == 'x86_64-pc-windows-gnu'
      run: |
        mkdir -p dist
        cp target/${{ matrix.target }}/release/ccometixline.exe dist/ccline.exe
        cd dist
        zip ../${{ matrix.name }} ccline.exe

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.name }}
        path: ${{ matrix.name }}

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: build
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts

    - name: Create Release
      uses: softprops/action-gh-release@v2
      with:
        files: artifacts/*/*
        generate_release_notes: true
        draft: false
        prerelease: false