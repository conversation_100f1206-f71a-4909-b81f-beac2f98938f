use super::Segment;
use crate::config::InputData;
use std::process::Command;
use std::time::Instant;

pub struct NetworkSegment {
    enabled: bool,
    target_host: String,
}

impl NetworkSegment {
    pub fn new(enabled: bool) -> Self {
        Self { 
            enabled,
            target_host: "api.packycode.com".to_string(),
        }
    }

    fn get_network_info(&self) -> NetworkInfo {
        let (latency, status) = match self.ping_host(&self.target_host) {
            Some(latency) => (Some(latency), NetworkStatus::Connected),
            None => (None, NetworkStatus::Unreachable),
        };

        NetworkInfo {
            host: self.target_host.clone(),
            latency,
            status,
        }
    }

    fn ping_host(&self, host: &str) -> Option<u32> {
        let start = Instant::now();
        
        let output = if cfg!(target_os = "windows") {
            Command::new("ping")
                .args(["-n", "1", "-w", "3000", host])
                .output()
                .ok()?
        } else {
            Command::new("ping")
                .args(["-c", "1", "-W", "3", host])
                .output()
                .ok()?
        };

        if output.status.success() {
            let latency = start.elapsed().as_millis() as u32;
            // 限制显示的延迟在合理范围内 (0-9999ms)
            Some(latency.min(9999))
        } else {
            None
        }
    }

    fn format_network_info(&self, info: &NetworkInfo) -> String {
        match info.status {
            NetworkStatus::Connected => {
                let latency_display = info.latency
                    .map(|ms| {
                        // 根据延迟给数值着色
                        if ms < 100 {
                            format!("\x1b[32m{}ms\x1b[0m", ms) // 绿色
                        } else if ms < 300 {
                            format!("\x1b[33m{}ms\x1b[0m", ms) // 黄色
                        } else {
                            format!("\x1b[31m{}ms\x1b[0m", ms) // 红色
                        }
                    })
                    .unwrap_or_else(|| "N/A".to_string());

                // 根据延迟选择图标
                let icon = info.latency.map(|ms| {
                    if ms < 100 {
                        "🟩" // 绿色方块 - 低延迟
                    } else if ms < 300 {
                        "🟨" // 黄色方块 - 中等延迟
                    } else {
                        "🟥" // 红色方块 - 高延迟
                    }
                }).unwrap_or("🟦"); // 蓝色方块 - 未知延迟

                // 简化域名显示
                let short_host = "packycode";
                format!("{} {} · {}", icon, short_host, latency_display)
            },
            NetworkStatus::Unreachable => {
                let short_host = "packycode";
                format!("🟥 {} · \x1b[31mUnreachable\x1b[0m", short_host)
            },
        }
    }
}

impl Segment for NetworkSegment {
    fn render(&self, _input: &InputData) -> String {
        if !self.enabled {
            return String::new();
        }

        let network_info = self.get_network_info();
        self.format_network_info(&network_info)
    }

    fn enabled(&self) -> bool {
        self.enabled
    }
}

#[derive(Debug)]
struct NetworkInfo {
    host: String,
    latency: Option<u32>, // 延迟(毫秒)
    status: NetworkStatus,
}

#[derive(Debug, PartialEq)]
enum NetworkStatus {
    Connected,     // 已连接且可达
    Unreachable,   // 不可达
}